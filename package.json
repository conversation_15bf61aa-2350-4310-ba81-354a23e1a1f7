{"name": "backend", "version": "1.0.0", "description": "back-end for my app", "main": "index.js", "scripts": {"start": "nodemon --inspect index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nh <EMAIL>", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.6.3", "nodemailer": "^6.10.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.0.1", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0"}, "devDependencies": {"morgan": "^1.10.0", "nodemon": "^3.1.6"}}