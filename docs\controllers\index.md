# Controllers Documentation

This directory contains documentation for all controllers in the application. Each controller is responsible for a specific area of functionality.

## Available Controllers

- [Bank Controller](bank.md) - Manages bank account operations
- [Calendar Controller](calendar.md) - Manages event scheduling and calendar functionality
- [Chat Controller](chat.md) - Handles real-time messaging functionality
- [Home Controller](home.md) - Provides dashboard metrics and reporting
- [Login Controller](login.md) - Handles user authentication and registration
- [Product Controller](product.md) - Manages product and supplier operations
- [Profile Controller](profile.md) - Manages user profile operations
- [Sell Controller](sell.md) - Handles sales operations and customer management
- [Supplier Controller](supplier.md) - Provides supplier search and suggestion functionality
- [User Controller](user.md) - Manages user operations and permissions

## Structure

Each controller documentation file follows a consistent structure:

1. **Overview** - A brief description of the controller's purpose and functionality
2. **Functions** - Detailed documentation for each function in the controller, including:
   - Description
   - Parameters
   - Process steps

## Usage

These documentation files serve as a reference for developers working with the application's backend. They provide a clear understanding of each controller's functionality without having to read through the code.